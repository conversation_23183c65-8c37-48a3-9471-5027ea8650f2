/**
 * Cookie安全工具类
 * 用于修复客户端Cookie引用安全漏洞
 * 提供安全的Cookie操作方法
 */

import Cookies from 'js-cookie'

// 安全配置常量
const SECURITY_CONFIG = {
  // 默认Cookie配置
  DEFAULT_OPTIONS: {
    secure: location.protocol === 'https:', // 根据协议动态设置
    sameSite: 'strict', // 防止CSRF攻击
    path: '/', // 明确设置路径
  },
  
  // Token相关配置
  TOKEN_OPTIONS: {
    secure: location.protocol === 'https:',
    sameSite: 'strict',
    expires: 1, // 1天过期
    path: '/',
  },
  
  // 应用状态相关配置
  APP_STATE_OPTIONS: {
    secure: location.protocol === 'https:',
    sameSite: 'strict',
    expires: 30, // 30天过期
    path: '/',
  },
  
  // 允许的Cookie名称白名单
  ALLOWED_COOKIES: [
    'vue_token',
    'sidebarStatus',
    'JSESSIONID',
    'zy_token'
  ],
  
  // 敏感Cookie名称（需要额外保护）
  SENSITIVE_COOKIES: [
    'vue_token',
    'zy_token'
  ]
}

/**
 * 验证Cookie名称是否在白名单中
 * @param {string} name Cookie名称
 * @returns {boolean} 是否允许
 */
function validateCookieName(name) {
  if (!name || typeof name !== 'string') {
    console.warn('Invalid cookie name provided')
    return false
  }
  
  return SECURITY_CONFIG.ALLOWED_COOKIES.includes(name)
}

/**
 * 验证Cookie值的安全性
 * @param {string} value Cookie值
 * @param {string} name Cookie名称
 * @returns {boolean} 是否安全
 */
function validateCookieValue(value, name) {
  if (value === null || value === undefined) {
    return false
  }
  
  if (typeof value !== 'string') {
    console.warn(`Invalid cookie value type for ${name}:`, typeof value)
    return false
  }
  
  // 检查是否包含危险字符
  const dangerousChars = /<script|javascript:|data:|vbscript:|onload|onerror/i
  if (dangerousChars.test(value)) {
    console.error(`Dangerous content detected in cookie ${name}`)
    return false
  }
  
  // 敏感Cookie的额外验证
  if (SECURITY_CONFIG.SENSITIVE_COOKIES.includes(name)) {
    if (value.length === 0 || value.length > 1000) {
      console.warn(`Invalid token length for ${name}`)
      return false
    }
  }
  
  return true
}

/**
 * 安全获取Cookie
 * @param {string} name Cookie名称
 * @returns {string|null} Cookie值或null
 */
export function secureCookieGet(name) {
  try {
    if (!validateCookieName(name)) {
      return null
    }
    
    const value = Cookies.get(name)
    
    if (!validateCookieValue(value, name)) {
      return null
    }
    
    return value
  } catch (error) {
    console.error(`Error getting cookie ${name}:`, error)
    return null
  }
}

/**
 * 安全设置Cookie
 * @param {string} name Cookie名称
 * @param {string} value Cookie值
 * @param {Object} options 额外选项
 * @returns {boolean} 是否设置成功
 */
export function secureCookieSet(name, value, options = {}) {
  try {
    if (!validateCookieName(name)) {
      return false
    }
    
    if (!validateCookieValue(value, name)) {
      return false
    }
    
    // 根据Cookie类型选择配置
    let defaultOptions
    if (SECURITY_CONFIG.SENSITIVE_COOKIES.includes(name)) {
      defaultOptions = SECURITY_CONFIG.TOKEN_OPTIONS
    } else {
      defaultOptions = SECURITY_CONFIG.APP_STATE_OPTIONS
    }
    
    const finalOptions = { ...defaultOptions, ...options }
    
    Cookies.set(name, value, finalOptions)
    return true
  } catch (error) {
    console.error(`Error setting cookie ${name}:`, error)
    return false
  }
}

/**
 * 安全删除Cookie
 * @param {string} name Cookie名称
 * @returns {boolean} 是否删除成功
 */
export function secureCookieRemove(name) {
  try {
    if (!validateCookieName(name)) {
      return false
    }
    
    Cookies.remove(name, { path: '/' })
    
    // 确保在不同路径下也删除
    Cookies.remove(name)
    
    return true
  } catch (error) {
    console.error(`Error removing cookie ${name}:`, error)
    return false
  }
}

/**
 * 清理所有应用相关的Cookie
 */
export function clearAllAppCookies() {
  try {
    SECURITY_CONFIG.ALLOWED_COOKIES.forEach(name => {
      secureCookieRemove(name)
    })
    console.log('All application cookies cleared')
  } catch (error) {
    console.error('Error clearing cookies:', error)
  }
}

/**
 * 检查Cookie安全状态
 * @returns {Object} 安全状态报告
 */
export function checkCookieSecurity() {
  const report = {
    isSecure: true,
    warnings: [],
    recommendations: []
  }
  
  // 检查是否在HTTPS环境
  if (location.protocol !== 'https:') {
    report.isSecure = false
    report.warnings.push('Application is not running over HTTPS')
    report.recommendations.push('Deploy application with HTTPS for enhanced security')
  }
  
  // 检查现有Cookie
  SECURITY_CONFIG.ALLOWED_COOKIES.forEach(name => {
    const value = Cookies.get(name)
    if (value && !validateCookieValue(value, name)) {
      report.isSecure = false
      report.warnings.push(`Potentially unsafe cookie detected: ${name}`)
    }
  })
  
  return report
}

/**
 * 初始化Cookie安全检查
 */
export function initCookieSecurity() {
  // 在开发环境下进行安全检查
  if (process.env.NODE_ENV === 'development') {
    const securityReport = checkCookieSecurity()
    if (!securityReport.isSecure) {
      console.warn('Cookie Security Report:', securityReport)
    }
  }
  
  // 设置全局错误处理
  window.addEventListener('error', (event) => {
    if (event.message && event.message.includes('cookie')) {
      console.error('Cookie-related error detected:', event.message)
    }
  })
}

export default {
  secureCookieGet,
  secureCookieSet,
  secureCookieRemove,
  clearAllAppCookies,
  checkCookieSecurity,
  initCookieSecurity
}

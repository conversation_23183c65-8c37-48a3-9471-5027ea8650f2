zy:
  name: "基于物联网彩票设备管理平台"
  author: "<PERSON><PERSON>"
  url: "http://localhost"
  timeout: 120 #session超时（分钟）
  job: true # 任务用标志
  #  sms:
  #    domain: "dysmsapi.aliyuncs.com"
  #    accessKeyId: "LTAI5tSbyrdRAuVXqsqcpUbd"
  #    accessKeySecret: "******************************"
  #    woSign: "海南体彩设备"
  #    woCode: "SMS_236875738" # 工单模板编号，工单号：152253869，参数：code
  sms:
    domain: "dysmsapi.aliyuncs.com"
    accessKeyId: "LTAI5tLVwFVnB9shckJQsxGK"
    accessKeySecret: "******************************"
    woSign: "海南体彩设备"
    woCode: "SMS_465398003" # 工单模板编号，工单号：20041603389，参数：code
  no: #编码规则类型定义
    asset: ASSET #资产
server:
  port: 8080
  servlet:
    context-path: /port-api
spring:
  main:
    banner-mode: "off"
    allow-bean-definition-overriding: true
  application:
    name: ia
  mvc:
    throw-exception-if-no-handler-found: true
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 256MB
  profiles:
    active: staging
  web:
    resources:
      add-mappings: false

#mybatis中mapper配置文件位置扫描
mybatis:
  config-location: classpath:mybatis-mysql.xml
  mapper-locations: classpath:**/dao/mapper/mysql/**/*.xml

jasper:
  path: .

weixin:
  appid: "wx6656f02e1b896e38"
  secret: "3a82f3ba9391256a7171a3dba2e20929"

amap:
  key: "b4ae4625e7f217801a29af656cbf8ce7"
